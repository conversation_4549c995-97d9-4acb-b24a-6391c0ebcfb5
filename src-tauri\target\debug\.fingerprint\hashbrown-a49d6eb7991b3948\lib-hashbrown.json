{"rustc": 1337386685567147700, "features": "[]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 2225463790103693989, "path": 14349441451025178862, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-a49d6eb7991b3948\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}