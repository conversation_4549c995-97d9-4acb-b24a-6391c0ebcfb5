{"rustc": 1337386685567147700, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2225463790103693989, "path": 6025109009999290925, "deps": [[2779309023524819297, "aho_corasick", false, 9098017300094096589], [7507008215594894126, "regex_syntax", false, 6079932146901551030], [15932120279885307830, "memchr", false, 147233583920824472], [16311927252525485886, "regex_automata", false, 1467049433014503288]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-2c33a0eeba3212dd\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}