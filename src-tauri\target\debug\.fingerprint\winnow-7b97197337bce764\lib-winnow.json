{"rustc": 1337386685567147700, "features": "[]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 9350918751530262658, "path": 14686324277928597561, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\winnow-7b97197337bce764\\dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}